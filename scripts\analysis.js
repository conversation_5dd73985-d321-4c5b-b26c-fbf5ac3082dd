// Analysis Page Specific Functionality
class AnalysisManager {
    constructor() {
        this.currentStock = '600519';
        this.analysisData = {};
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeAnalysisData();
        this.startRealTimeUpdates();
        this.setupSentimentAnimation();
        this.setupThemeCloudInteraction();
    }

    setupEventListeners() {
        // Stock selector change
        const stockSelect = document.getElementById('stockSelect');
        if (stockSelect) {
            stockSelect.addEventListener('change', (e) => {
                this.currentStock = e.target.value;
                this.updateAnalysisData();
                this.animateDataUpdate();
            });
        }

        // Language change listener
        window.addEventListener('languageChanged', () => {
            this.updateAnalysisContent();
        });

        // Theme tag hover effects
        document.querySelectorAll('.theme-tag').forEach(tag => {
            tag.addEventListener('mouseenter', () => {
                this.highlightTheme(tag);
            });

            tag.addEventListener('mouseleave', () => {
                this.unhighlightTheme(tag);
            });
        });
    }

    initializeAnalysisData() {
        this.analysisData = {
            '600519': {
                patterns: ['hammer', 'consolidation'],
                sentiment: 0.75,
                themes: [
                    { text: '高端化', weight: 'high', sentiment: 'positive' },
                    { text: '品牌价值', weight: 'medium', sentiment: 'positive' },
                    { text: '业绩增长', weight: 'high', sentiment: 'positive' },
                    { text: '市场扩张', weight: 'low', sentiment: 'neutral' },
                    { text: '创新驱动', weight: 'medium', sentiment: 'positive' }
                ],
                narrative: '公司Q3业绩稳健增长，高端产品销售强劲，品牌价值持续提升，市场地位进一步巩固',
                trendInsight: '当前处于短期调整阶段，成交量温和放大，显示资金关注度提升',
                supportLevel: 1680,
                resistanceLevel: 1750
            },
            '000858': {
                patterns: ['doji', 'support'],
                sentiment: 0.65,
                themes: [
                    { text: '产品升级', weight: 'high', sentiment: 'positive' },
                    { text: '渠道拓展', weight: 'medium', sentiment: 'positive' },
                    { text: '成本控制', weight: 'medium', sentiment: 'neutral' },
                    { text: '竞争加剧', weight: 'low', sentiment: 'negative' }
                ],
                narrative: '公司持续推进产品结构升级，渠道建设稳步推进，盈利能力保持稳定',
                trendInsight: '技术面呈现震荡整理格局，等待方向性突破',
                supportLevel: 175,
                resistanceLevel: 190
            },
            '000001': {
                patterns: ['ascending_triangle', 'volume_surge'],
                sentiment: 0.55,
                themes: [
                    { text: '数字化转型', weight: 'high', sentiment: 'positive' },
                    { text: '风险管控', weight: 'medium', sentiment: 'neutral' },
                    { text: '息差压力', weight: 'medium', sentiment: 'negative' },
                    { text: '零售业务', weight: 'high', sentiment: 'positive' }
                ],
                narrative: '银行数字化转型成效显著，零售业务增长强劲，但面临息差收窄压力',
                trendInsight: '股价突破前期阻力位，成交量配合良好，短期看涨',
                supportLevel: 11.5,
                resistanceLevel: 13.2
            },
            '600036': {
                patterns: ['flag', 'breakout'],
                sentiment: 0.70,
                themes: [
                    { text: '财富管理', weight: 'high', sentiment: 'positive' },
                    { text: '科技赋能', weight: 'medium', sentiment: 'positive' },
                    { text: '资产质量', weight: 'medium', sentiment: 'positive' },
                    { text: '监管政策', weight: 'low', sentiment: 'neutral' }
                ],
                narrative: '财富管理业务快速发展，科技投入持续加大，资产质量保持优良',
                trendInsight: '技术面形成旗形整理，有望向上突破',
                supportLevel: 33.5,
                resistanceLevel: 37.8
            }
        };

        this.updateAnalysisData();
    }

    updateAnalysisData() {
        const data = this.analysisData[this.currentStock];
        if (!data) return;

        this.updatePatternTags(data.patterns);
        this.updateSentimentBar(data.sentiment);
        this.updateThemeCloud(data.themes);
        this.updateNarrativeContent(data);
        this.updatePriceZones(data.supportLevel, data.resistanceLevel);
    }

    updatePatternTags(patterns) {
        const patternTags = document.querySelector('.pattern-tags');
        if (!patternTags) return;

        const isZh = window.languageManager?.getCurrentLanguage() === 'zh';
        const patternNames = {
            hammer: isZh ? '支撑位锤头线' : 'Hammer at Support',
            consolidation: isZh ? '整理形态' : 'Consolidation',
            doji: isZh ? '十字星' : 'Doji',
            support: isZh ? '支撑确认' : 'Support Confirmed',
            ascending_triangle: isZh ? '上升三角形' : 'Ascending Triangle',
            volume_surge: isZh ? '放量突破' : 'Volume Surge',
            flag: isZh ? '旗形整理' : 'Flag Pattern',
            breakout: isZh ? '向上突破' : 'Breakout'
        };

        patternTags.innerHTML = '';
        patterns.forEach(pattern => {
            const tag = document.createElement('span');
            tag.className = 'pattern-tag bullish';
            tag.textContent = patternNames[pattern] || pattern;
            patternTags.appendChild(tag);
        });
    }

    updateSentimentBar(sentiment) {
        const sentimentFill = document.querySelector('.sentiment-fill');
        const sentimentLabel = document.querySelector('.sentiment-label');
        
        if (sentimentFill && sentimentLabel) {
            const percentage = sentiment * 100;
            sentimentFill.style.width = `${percentage}%`;
            
            const isZh = window.languageManager?.getCurrentLanguage() === 'zh';
            let sentimentText = '';
            
            if (sentiment > 0.7) {
                sentimentText = isZh ? '整体情绪：乐观' : 'Overall Sentiment: Optimistic';
            } else if (sentiment > 0.5) {
                sentimentText = isZh ? '整体情绪：中性偏乐观' : 'Overall Sentiment: Neutral-Positive';
            } else if (sentiment > 0.3) {
                sentimentText = isZh ? '整体情绪：中性' : 'Overall Sentiment: Neutral';
            } else {
                sentimentText = isZh ? '整体情绪：谨慎' : 'Overall Sentiment: Cautious';
            }
            
            sentimentLabel.textContent = sentimentText;
        }
    }

    updateThemeCloud(themes) {
        const themeCloud = document.querySelector('.theme-cloud');
        if (!themeCloud) return;

        const isZh = window.languageManager?.getCurrentLanguage() === 'zh';
        
        themeCloud.innerHTML = '';
        themes.forEach(theme => {
            const tag = document.createElement('span');
            tag.className = `theme-tag ${theme.weight}`;
            tag.textContent = theme.text;
            tag.setAttribute('data-sentiment', theme.sentiment);
            themeCloud.appendChild(tag);
        });
    }

    updateNarrativeContent(data) {
        const narrativeSummary = document.querySelector('.narrative-summary');
        const insightText = document.querySelector('.insight-text');
        
        if (narrativeSummary) {
            narrativeSummary.textContent = data.narrative;
        }
        
        if (insightText) {
            insightText.textContent = data.trendInsight;
        }
    }

    updatePriceZones(support, resistance) {
        const supportPrice = document.querySelector('.price-zone.support .zone-price');
        const resistancePrice = document.querySelector('.price-zone.resistance .zone-price');
        
        if (supportPrice) {
            supportPrice.textContent = `¥${support}`;
        }
        
        if (resistancePrice) {
            resistancePrice.textContent = `¥${resistance}`;
        }
    }

    setupSentimentAnimation() {
        const sentimentWave = document.querySelector('.sentiment-wave');
        if (sentimentWave) {
            // Animate the sentiment wave based on sentiment strength
            setInterval(() => {
                const data = this.analysisData[this.currentStock];
                if (data) {
                    const speed = 2000 + (1 - data.sentiment) * 1000; // Slower for more positive sentiment
                    sentimentWave.style.animationDuration = `${speed}ms`;
                }
            }, 5000);
        }
    }

    setupThemeCloudInteraction() {
        // Add interactive effects to theme tags
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('theme-tag')) {
                this.showThemeDetails(e.target);
            }
        });
    }

    highlightTheme(tag) {
        tag.style.transform = 'scale(1.1)';
        tag.style.zIndex = '10';
        
        // Add glow effect based on sentiment
        const sentiment = tag.getAttribute('data-sentiment');
        if (sentiment === 'positive') {
            tag.style.boxShadow = '0 0 15px rgba(0, 255, 136, 0.6)';
        } else if (sentiment === 'negative') {
            tag.style.boxShadow = '0 0 15px rgba(255, 100, 100, 0.6)';
        } else {
            tag.style.boxShadow = '0 0 15px rgba(0, 212, 255, 0.6)';
        }
    }

    unhighlightTheme(tag) {
        tag.style.transform = 'scale(1)';
        tag.style.zIndex = 'auto';
        tag.style.boxShadow = '';
    }

    showThemeDetails(tag) {
        // Create a tooltip or modal with theme details
        const tooltip = document.createElement('div');
        tooltip.className = 'theme-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <h4>${tag.textContent}</h4>
                <p>相关度: ${tag.classList.contains('high') ? '高' : tag.classList.contains('medium') ? '中' : '低'}</p>
                <p>情绪倾向: ${tag.getAttribute('data-sentiment')}</p>
            </div>
        `;
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = tag.getBoundingClientRect();
        tooltip.style.position = 'fixed';
        tooltip.style.left = `${rect.left}px`;
        tooltip.style.top = `${rect.bottom + 10}px`;
        tooltip.style.zIndex = '1000';
        
        // Remove tooltip after 3 seconds
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 3000);
    }

    animateDataUpdate() {
        // Animate the update of analysis data
        const modules = document.querySelectorAll('.analysis-module');
        
        modules.forEach((module, index) => {
            module.style.opacity = '0.7';
            module.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                module.style.opacity = '1';
                module.style.transform = 'translateY(0)';
            }, index * 200);
        });
    }

    startRealTimeUpdates() {
        // Simulate real-time updates every 30 seconds
        this.updateInterval = setInterval(() => {
            this.simulateDataUpdate();
        }, 30000);
    }

    simulateDataUpdate() {
        // Simulate small changes in sentiment and other metrics
        Object.keys(this.analysisData).forEach(stock => {
            const data = this.analysisData[stock];
            
            // Small random changes to sentiment
            data.sentiment += (Math.random() - 0.5) * 0.1;
            data.sentiment = Math.max(0, Math.min(1, data.sentiment));
            
            // Small changes to price levels
            data.supportLevel += (Math.random() - 0.5) * 5;
            data.resistanceLevel += (Math.random() - 0.5) * 5;
        });
        
        // Update current stock data
        this.updateAnalysisData();
        
        // Animate the update
        this.animateDataUpdate();
    }

    updateAnalysisContent() {
        // Update content based on language change
        this.updateAnalysisData();
    }

    // Public methods
    setStock(stockCode) {
        this.currentStock = stockCode;
        this.updateAnalysisData();
        this.animateDataUpdate();
    }

    refreshAnalysis() {
        this.simulateDataUpdate();
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize analysis manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.analysis-page')) {
        window.analysisManager = new AnalysisManager();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnalysisManager;
}
