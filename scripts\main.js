// Main Application Controller
class HuiShiApp {
    constructor() {
        this.isLoaded = false;
        this.currentPage = this.getCurrentPage();
        this.components = {};
        this.init();
    }

    init() {
        this.setupGlobalEventListeners();
        this.initializeComponents();
        this.setupPageSpecificFeatures();
        this.handleInitialLoad();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('analysis.html')) return 'analysis';
        return 'home';
    }

    setupGlobalEventListeners() {
        // Page load event
        window.addEventListener('load', () => {
            this.handlePageLoad();
        });

        // Resize event
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Scroll event
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16));

        // Visibility change (for performance optimization)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Error handling
        window.addEventListener('error', (e) => {
            this.handleError(e);
        });

        // Unhandled promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            this.handleError(e);
        });
    }

    initializeComponents() {
        // Initialize global components
        this.components.language = window.languageManager;
        this.components.animation = window.animationManager;
        
        // Initialize page-specific components
        if (this.currentPage === 'home') {
            this.components.neural = window.neuralBackground;
        } else if (this.currentPage === 'analysis') {
            this.components.charts = window.chartManager;
            this.components.analysis = window.analysisManager;
        }
    }

    setupPageSpecificFeatures() {
        if (this.currentPage === 'home') {
            this.setupHomePage();
        } else if (this.currentPage === 'analysis') {
            this.setupAnalysisPage();
        }
    }

    setupHomePage() {
        // Setup CTA button interaction
        const ctaButton = document.querySelector('.cta-button');
        if (ctaButton) {
            ctaButton.addEventListener('click', () => {
                this.navigateToAnalysis();
            });
        }

        // Setup feature card interactions
        this.setupFeatureCards();
        
        // Setup Web3 section animations
        this.setupWeb3Animations();
    }

    setupAnalysisPage() {
        // Setup stock selector enhancements
        this.enhanceStockSelector();
        
        // Setup AI status indicator
        this.setupAIStatusIndicator();
        
        // Setup probability bar animations
        this.setupProbabilityAnimations();
    }

    setupFeatureCards() {
        const featureCards = document.querySelectorAll('.feature-card');
        
        featureCards.forEach((card, index) => {
            // Add entrance animation delay
            card.style.animationDelay = `${index * 0.2}s`;
            
            // Add click interaction
            card.addEventListener('click', () => {
                this.handleFeatureCardClick(card, index);
            });
        });
    }

    setupWeb3Animations() {
        const blockchainNodes = document.querySelectorAll('.node');
        
        blockchainNodes.forEach((node, index) => {
            // Add interactive hover effects
            node.addEventListener('mouseenter', () => {
                node.style.transform = 'scale(1.2)';
                node.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.8)';
            });
            
            node.addEventListener('mouseleave', () => {
                node.style.transform = 'scale(1)';
                node.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.3)';
            });
        });
    }

    enhanceStockSelector() {
        const stockSelect = document.getElementById('stockSelect');
        if (!stockSelect) return;

        // Add custom styling and animations
        stockSelect.addEventListener('change', () => {
            this.animateStockChange();
        });

        // Add keyboard navigation
        stockSelect.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.animateStockChange();
            }
        });
    }

    setupAIStatusIndicator() {
        const statusIndicator = document.querySelector('.status-indicator');
        if (!statusIndicator) return;

        // Animate the AI status indicator
        setInterval(() => {
            statusIndicator.style.opacity = '0.3';
            setTimeout(() => {
                statusIndicator.style.opacity = '1';
            }, 500);
        }, 2000);
    }

    setupProbabilityAnimations() {
        const probBars = document.querySelectorAll('.prob-fill');
        
        probBars.forEach(bar => {
            // Add particle effect
            this.addParticleEffect(bar);
        });
    }

    addParticleEffect(element) {
        const particles = element.querySelector('.prob-particles');
        if (!particles) return;

        // Animate particles flowing through the probability bar
        setInterval(() => {
            particles.style.animation = 'none';
            setTimeout(() => {
                particles.style.animation = 'particleFlow 2s linear infinite';
            }, 100);
        }, 3000);
    }

    // Event Handlers
    handlePageLoad() {
        this.isLoaded = true;
        document.body.classList.add('loaded');
        
        // Trigger entrance animations
        this.triggerEntranceAnimations();
        
        // Initialize performance monitoring
        this.initPerformanceMonitoring();
    }

    handleResize() {
        // Update component sizes
        if (this.components.neural) {
            this.components.neural.setupCanvas();
        }
        
        if (this.components.charts) {
            Object.values(this.components.charts.charts).forEach(chart => {
                if (chart) chart.resize();
            });
        }
    }

    handleScroll() {
        const scrolled = window.pageYOffset;
        
        // Update navigation background
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            const opacity = Math.min(scrolled / 100, 0.95);
            navbar.style.background = `rgba(10, 10, 10, ${opacity})`;
        }
        
        // Parallax effects
        this.updateParallaxElements(scrolled);
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Pause animations when page is not visible
            this.pauseAnimations();
        } else {
            // Resume animations when page becomes visible
            this.resumeAnimations();
        }
    }

    handleError(error) {
        console.error('HuiShi App Error:', error);
        
        // Show user-friendly error message
        this.showErrorMessage('发生了一个错误，请刷新页面重试。');
    }

    // Navigation
    navigateToAnalysis() {
        // Add transition effect
        document.body.style.opacity = '0.8';
        document.body.style.transform = 'scale(0.98)';
        
        setTimeout(() => {
            window.location.href = 'analysis.html';
        }, 300);
    }

    // Animations
    triggerEntranceAnimations() {
        const animatedElements = document.querySelectorAll('[data-aos]');
        
        animatedElements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('aos-animate');
            }, index * 100);
        });
    }

    animateStockChange() {
        const analysisModules = document.querySelectorAll('.analysis-module');
        
        analysisModules.forEach((module, index) => {
            module.style.opacity = '0.7';
            module.style.transform = 'translateX(-10px)';
            
            setTimeout(() => {
                module.style.opacity = '1';
                module.style.transform = 'translateX(0)';
            }, index * 150);
        });
    }

    updateParallaxElements(scrolled) {
        const parallaxElements = document.querySelectorAll('.parallax');
        
        parallaxElements.forEach(element => {
            const speed = element.getAttribute('data-speed') || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }

    pauseAnimations() {
        if (this.components.neural) {
            this.components.neural.pause();
        }
        
        // Pause CSS animations
        document.body.style.animationPlayState = 'paused';
    }

    resumeAnimations() {
        if (this.components.neural) {
            this.components.neural.resume();
        }
        
        // Resume CSS animations
        document.body.style.animationPlayState = 'running';
    }

    // User Interactions
    handleFeatureCardClick(card, index) {
        // Add click animation
        card.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            card.style.transform = 'scale(1)';
            
            // Navigate based on card index
            if (index === 0 || index === 1 || index === 2) {
                this.navigateToAnalysis();
            }
        }, 150);
    }

    // Utility Methods
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <p>${message}</p>
                <button onclick="this.parentElement.parentElement.remove()">关闭</button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    initPerformanceMonitoring() {
        // Monitor performance metrics
        if ('performance' in window) {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log(`Page load time: ${loadTime}ms`);
        }
    }

    handleInitialLoad() {
        // Add initial loading animation
        document.body.classList.add('loading');
        
        setTimeout(() => {
            document.body.classList.remove('loading');
            document.body.classList.add('loaded');
        }, 1000);
    }

    // Utility functions
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public API
    getComponent(name) {
        return this.components[name];
    }

    updateLanguage(language) {
        if (this.components.language) {
            this.components.language.switchLanguage(language);
        }
    }

    destroy() {
        // Cleanup all components
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.huiShiApp = new HuiShiApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HuiShiApp;
}
