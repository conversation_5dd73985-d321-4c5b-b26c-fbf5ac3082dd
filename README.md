# 「慧视」AI金融洞察引擎 | HuiShi AI Financial Insight Engine

一个现代化的双语AI金融分析平台，提供K线模式识别、财报叙事分析和智能投资洞察。

A modern bilingual AI financial analysis platform providing K-line pattern recognition, financial narrative analysis, and intelligent investment insights.

## 🌟 特性 | Features

### 🎨 设计特色 | Design Features
- **深色主题设计** - 深邃的宇宙黑背景配合科技蓝、AI绿、神秘紫点缀
- **Dark Theme Design** - Deep cosmic black background with tech-blue, AI-green, and mystery-purple accents
- **流畅动画效果** - 微动画、悬停效果、平滑过渡
- **Smooth Animations** - Micro-animations, hover effects, smooth transitions
- **响应式布局** - 适配各种屏幕尺寸
- **Responsive Layout** - Adapts to all screen sizes

### 🧠 AI功能 | AI Features
- **K线模式识别** - AI识别技术形态和价格模式
- **K-line Pattern Recognition** - AI identifies technical patterns and price formations
- **财报叙事分析** - 智能解析财报核心叙事和情绪
- **Financial Narrative Analysis** - Intelligent analysis of financial report narratives and sentiment
- **概率统计分析** - 基于历史数据的概率预测
- **Probability Analysis** - Historical data-based probability predictions
- **实时数据更新** - 模拟实时市场数据更新
- **Real-time Updates** - Simulated real-time market data updates

### 🌐 双语支持 | Bilingual Support
- **中英文切换** - 一键切换语言，内容平滑过渡
- **Chinese/English Toggle** - One-click language switching with smooth content transitions
- **本地化存储** - 记住用户语言偏好
- **Localized Storage** - Remembers user language preference

### 🚀 Web3愿景 | Web3 Vision
- **未来探索** - 展示AI与Web3技术融合的愿景
- **Future Exploration** - Showcases the vision of AI and Web3 technology integration

## 📁 项目结构 | Project Structure

```
huishiai/
├── index.html              # 主页 | Homepage
├── analysis.html           # AI分析页面 | AI Analysis Page
├── README.md               # 项目文档 | Project Documentation
├── styles/                 # 样式文件 | Stylesheets
│   ├── main.css           # 主样式 | Main Styles
│   ├── analysis.css       # 分析页样式 | Analysis Page Styles
│   └── animations.css     # 动画样式 | Animation Styles
└── scripts/               # JavaScript文件 | JavaScript Files
    ├── main.js           # 主应用控制器 | Main App Controller
    ├── language.js       # 语言管理 | Language Management
    ├── neural-background.js # 神经网络背景 | Neural Network Background
    ├── animations.js     # 动画管理 | Animation Management
    ├── charts.js         # 图表管理 | Chart Management
    └── analysis.js       # 分析页功能 | Analysis Page Features
```

## 🎯 页面功能 | Page Features

### 主页 | Homepage (`index.html`)
- **英雄区域** - 动态神经网络背景，主标题和CTA按钮
- **Hero Section** - Dynamic neural network background, main title and CTA button
- **核心功能展示** - 三个主要功能模块的介绍卡片
- **Key Features** - Introduction cards for three main functional modules
- **Web3愿景** - 未来技术发展方向展示
- **Web3 Vision** - Future technology development showcase

### AI分析页 | AI Analysis Page (`analysis.html`)
- **股票选择器** - 支持多只股票切换
- **Stock Selector** - Supports multiple stock switching
- **K线图表** - 日/周/月线图表，带AI模式标注
- **K-line Charts** - Daily/Weekly/Monthly charts with AI pattern annotations
- **成交量分析** - 成交量图表和分析
- **Volume Analysis** - Volume charts and analysis
- **AI洞察面板** - 模式识别、概率分析、情绪分析
- **AI Insight Panel** - Pattern recognition, probability analysis, sentiment analysis

## 🛠️ 技术栈 | Tech Stack

- **HTML5** - 语义化标记
- **CSS3** - 现代样式，Flexbox/Grid布局，CSS动画
- **JavaScript (ES6+)** - 模块化架构，类组件
- **Chart.js** - 图表渲染
- **Web APIs** - Intersection Observer, Canvas API
- **响应式设计** - 移动端优先

## 🎨 设计系统 | Design System

### 色彩方案 | Color Palette
```css
--bg-primary: #0a0a0a        /* 主背景 | Primary Background */
--bg-secondary: #1a1a1a      /* 次背景 | Secondary Background */
--tech-blue: #00d4ff         /* 科技蓝 | Tech Blue */
--ai-green: #00ff88          /* AI绿 | AI Green */
--mystery-purple: #8b5cf6    /* 神秘紫 | Mystery Purple */
--accent-gold: #ffd700       /* 强调金 | Accent Gold */
```

### 字体 | Typography
- **主字体** - Inter (现代无衬线字体)
- **代码字体** - JetBrains Mono (等宽字体)

## 🚀 快速开始 | Quick Start

1. **克隆项目** | Clone the project
```bash
git clone [repository-url]
cd huishiai
```

2. **打开项目** | Open the project
- 直接在浏览器中打开 `index.html`
- 或使用本地服务器 (推荐)

3. **本地服务器** | Local Server (推荐 | Recommended)
```bash
# 使用 Python
python -m http.server 8000

# 使用 Node.js
npx serve .

# 使用 Live Server (VS Code扩展)
```

4. **访问网站** | Access the website
- 主页: `http://localhost:8000/index.html`
- 分析页: `http://localhost:8000/analysis.html`

## 📱 响应式设计 | Responsive Design

网站支持以下设备：
- **桌面端** - 1200px及以上
- **平板端** - 768px - 1199px  
- **手机端** - 767px及以下

## 🔧 自定义配置 | Customization

### 修改颜色主题 | Modify Color Theme
在 `styles/main.css` 中修改 CSS 变量：
```css
:root {
    --tech-blue: #your-color;
    --ai-green: #your-color;
    /* ... */
}
```

### 添加新股票 | Add New Stocks
在 `scripts/charts.js` 中的 `generateMockData()` 方法中添加：
```javascript
const stocks = {
    'your-code': { name: '股票名称', basePrice: 100 },
    // ...
};
```

### 修改语言内容 | Modify Language Content
在HTML元素中使用 `data-zh` 和 `data-en` 属性：
```html
<span data-zh="中文内容" data-en="English Content">中文内容</span>
```

## 🎭 动画效果 | Animation Effects

- **入场动画** - 元素滚动进入视窗时触发
- **悬停效果** - 鼠标悬停时的微交互
- **加载动画** - 数据加载时的过渡效果
- **神经网络背景** - 交互式粒子动画

## 📊 数据说明 | Data Information

**重要提示**: 本项目使用模拟数据，仅用于演示AI技术能力，不构成任何投资建议。

**Important Note**: This project uses simulated data for AI technology demonstration only and does not constitute investment advice.

## 🔮 未来计划 | Future Plans

- [ ] 集成真实金融数据API
- [ ] 添加更多AI分析模型
- [ ] 实现Web3功能集成
- [ ] 添加用户账户系统
- [ ] 移动端原生应用

## 📄 许可证 | License

本项目仅用于演示和学习目的。

This project is for demonstration and learning purposes only.

## 🤝 贡献 | Contributing

欢迎提交问题和改进建议！

Welcome to submit issues and improvement suggestions!

---

**免责声明**: 本网站内容仅用于AI技术演示，不构成投资建议。所有分析结果基于历史数据，不保证未来表现。

**Disclaimer**: This website content is for AI technology demonstration only and does not constitute investment advice. All analysis results are based on historical data and do not guarantee future performance.
