// Language System for Bilingual Support
class LanguageManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('huishi-language') || 'zh';
        this.translations = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadLanguage(this.currentLanguage);
        this.updateLanguageToggle();
    }

    setupEventListeners() {
        const langToggle = document.getElementById('langToggle');
        if (langToggle) {
            langToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
    }

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'zh' ? 'en' : 'zh';
        this.switchLanguage(newLanguage);
    }

    switchLanguage(language) {
        if (language === this.currentLanguage) return;
        
        this.currentLanguage = language;
        localStorage.setItem('huishi-language', language);
        
        // Update HTML lang attribute
        document.documentElement.lang = language === 'zh' ? 'zh-CN' : 'en';
        
        // Update all elements with data attributes
        this.updateContent();
        this.updateLanguageToggle();
        
        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: language }
        }));
    }

    loadLanguage(language) {
        this.currentLanguage = language;
        document.documentElement.lang = language === 'zh' ? 'zh-CN' : 'en';
        this.updateContent();
    }

    updateContent() {
        const elements = document.querySelectorAll('[data-zh][data-en]');
        
        elements.forEach(element => {
            const zhText = element.getAttribute('data-zh');
            const enText = element.getAttribute('data-en');
            
            if (zhText && enText) {
                const newText = this.currentLanguage === 'zh' ? zhText : enText;
                
                // Smooth transition effect
                element.style.opacity = '0.7';
                element.style.transform = 'translateY(-2px)';
                
                setTimeout(() => {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.placeholder = newText;
                    } else if (element.tagName === 'OPTION') {
                        element.textContent = newText;
                    } else {
                        element.textContent = newText;
                    }
                    
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 150);
            }
        });

        // Update page title
        this.updatePageTitle();
        
        // Update meta description
        this.updateMetaDescription();
    }

    updatePageTitle() {
        const titleMap = {
            'index.html': {
                zh: '「慧视」AI金融洞察引擎 | 智能投资分析平台',
                en: 'HuiShi AI Financial Insight Engine | Intelligent Investment Analysis Platform'
            },
            'analysis.html': {
                zh: 'AI分析 | 「慧视」AI金融洞察引擎',
                en: 'AI Analysis | HuiShi AI Financial Insight Engine'
            }
        };

        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const titles = titleMap[currentPage];
        
        if (titles) {
            document.title = this.currentLanguage === 'zh' ? titles.zh : titles.en;
        }
    }

    updateMetaDescription() {
        const descriptionMap = {
            'index.html': {
                zh: 'AI驱动的金融洞察平台，提供K线模式识别、财报叙事分析和智能投资建议。探索Web3金融科技的未来。',
                en: 'AI-powered financial insight platform providing K-line pattern recognition, financial narrative analysis, and intelligent investment advice. Explore the future of Web3 fintech.'
            },
            'analysis.html': {
                zh: '实时AI分析K线模式和金融叙事，提供基于历史数据的概率统计和智能洞察。',
                en: 'Real-time AI analysis of K-line patterns and financial narratives with probability statistics and intelligent insights based on historical data.'
            }
        };

        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const descriptions = descriptionMap[currentPage];
        
        if (descriptions) {
            const metaDescription = document.querySelector('meta[name="description"]');
            if (metaDescription) {
                metaDescription.setAttribute('content', 
                    this.currentLanguage === 'zh' ? descriptions.zh : descriptions.en
                );
            }
        }
    }

    updateLanguageToggle() {
        const langToggle = document.getElementById('langToggle');
        const langText = langToggle?.querySelector('.lang-text');
        
        if (langText) {
            const zhText = langText.getAttribute('data-zh');
            const enText = langText.getAttribute('data-en');
            
            if (zhText && enText) {
                langText.textContent = this.currentLanguage === 'zh' ? zhText : enText;
            }
        }
    }

    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Method to get translated text programmatically
    getText(zhText, enText) {
        return this.currentLanguage === 'zh' ? zhText : enText;
    }

    // Method to format numbers based on language
    formatNumber(number) {
        if (this.currentLanguage === 'zh') {
            return new Intl.NumberFormat('zh-CN').format(number);
        } else {
            return new Intl.NumberFormat('en-US').format(number);
        }
    }

    // Method to format currency based on language
    formatCurrency(amount, currency = 'CNY') {
        const options = {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        };

        if (this.currentLanguage === 'zh') {
            return new Intl.NumberFormat('zh-CN', options).format(amount);
        } else {
            return new Intl.NumberFormat('en-US', options).format(amount);
        }
    }

    // Method to format dates based on language
    formatDate(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        if (this.currentLanguage === 'zh') {
            return new Intl.DateTimeFormat('zh-CN', options).format(date);
        } else {
            return new Intl.DateTimeFormat('en-US', options).format(date);
        }
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageManager;
}
