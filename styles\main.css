/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --tech-blue: #00d4ff;
    --ai-green: #00ff88;
    --mystery-purple: #8b5cf6;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-tertiary: #808080;
    --accent-gold: #ffd700;
    --accent-cyan: #00ffff;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--tech-blue), var(--mystery-purple));
    --gradient-secondary: linear-gradient(135deg, var(--ai-green), var(--tech-blue));
    --gradient-gold: linear-gradient(135deg, var(--accent-gold), #ffb347);
    
    /* Shadows and Glows */
    --glow-blue: 0 0 20px rgba(0, 212, 255, 0.3);
    --glow-green: 0 0 20px rgba(0, 255, 136, 0.3);
    --glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
    --glow-gold: 0 0 30px rgba(255, 215, 0, 0.4);
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Language Toggle */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.lang-btn {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid var(--tech-blue);
    color: var(--tech-blue);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-family: var(--font-primary);
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lang-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    box-shadow: var(--glow-blue);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--glow-blue);
}

.logo-subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: -4px;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--tech-blue);
    text-shadow: var(--glow-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.neural-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#neuralCanvas {
    width: 100%;
    height: 100%;
    opacity: 0.6;
}

.hero-content {
    text-align: center;
    z-index: 2;
    position: relative;
    max-width: 800px;
    padding: 0 2rem;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-main {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--glow-blue);
    display: block;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    color: var(--text-primary);
    margin-bottom: 3rem;
    line-height: 1.4;
}

.hero-features {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-item {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.feature-separator {
    color: var(--tech-blue);
    font-weight: 300;
}

/* CTA Button */
.cta-button {
    position: relative;
    background: var(--gradient-gold);
    border: none;
    padding: 1rem 3rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--bg-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--glow-gold);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.cta-button:hover .button-glow {
    left: 100%;
}

.disclaimer {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    font-style: italic;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Section */
.features {
    padding: 8rem 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.feature-card {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--glow-blue);
    border-color: var(--tech-blue);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 50%;
    color: var(--text-primary);
}

.feature-icon svg {
    width: 40px;
    height: 40px;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Web3 Vision Section */
.web3-vision {
    padding: 8rem 0;
    background: var(--bg-secondary);
}

.web3-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.web3-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
}

.web3-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.blockchain-nodes {
    position: relative;
    width: 300px;
    height: 300px;
}

.node {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    box-shadow: var(--glow-blue);
    animation: nodeFloat 3s ease-in-out infinite;
}

.node:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); animation-delay: 0s; }
.node:nth-child(2) { top: 25%; right: 0; animation-delay: 0.6s; }
.node:nth-child(3) { bottom: 25%; right: 0; animation-delay: 1.2s; }
.node:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 1.8s; }
.node:nth-child(5) { top: 25%; left: 0; animation-delay: 2.4s; }

/* Footer */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid rgba(0, 212, 255, 0.2);
    padding: 3rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-disclaimer p {
    color: var(--text-primary);
    font-weight: 500;
}

.footer-links span {
    color: var(--text-tertiary);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .language-toggle {
        top: 15px;
        right: 15px;
    }

    .lang-btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .nav-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        font-size: 0.9rem;
    }

    .hero {
        padding: 0 1rem;
        min-height: 90vh;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .title-main {
        font-size: clamp(2rem, 8vw, 3rem);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: clamp(1rem, 4vw, 1.2rem);
        margin-bottom: 2rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }

    .feature-separator {
        display: none;
    }

    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .features {
        padding: 4rem 0;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }

    .feature-card {
        padding: 2rem 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
    }

    .feature-icon svg {
        width: 30px;
        height: 30px;
    }

    .feature-title {
        font-size: 1.3rem;
    }

    .web3-vision {
        padding: 4rem 0;
    }

    .web3-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .web3-text p {
        font-size: 1rem;
        line-height: 1.6;
    }

    .blockchain-nodes {
        width: 250px;
        height: 250px;
        margin: 0 auto;
    }

    .node {
        width: 40px;
        height: 40px;
    }

    .footer {
        padding: 2rem 0;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-disclaimer p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 0 0.5rem;
    }

    .title-main {
        font-size: clamp(1.8rem, 7vw, 2.5rem);
    }

    .hero-subtitle {
        font-size: clamp(0.9rem, 3.5vw, 1.1rem);
    }

    .cta-button {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
    }

    .feature-card {
        padding: 1.5rem 1rem;
    }

    .blockchain-nodes {
        width: 200px;
        height: 200px;
    }

    .node {
        width: 30px;
        height: 30px;
    }
}

/* Animations */
@keyframes nodeFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.1); }
}

/* Loading States */
.loading {
    opacity: 0;
    transition: opacity 0.5s ease;
}

.loaded {
    opacity: 1;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid var(--tech-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Messages */
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 100, 100, 0.9);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid #ff6464;
    z-index: 10000;
    max-width: 300px;
    animation: slideInRight 0.3s ease;
}

.error-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.error-content button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.error-content button:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Theme Tooltip */
.theme-tooltip {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid var(--tech-blue);
    border-radius: 10px;
    padding: 1rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    box-shadow: var(--glow-blue);
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.3s ease;
}

.tooltip-content h4 {
    color: var(--tech-blue);
    margin-bottom: 0.5rem;
}

/* Utility Classes */
.fade-in {
    opacity: 0;
    animation: fadeIn 1s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
