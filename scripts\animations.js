// Animation System for Smooth Interactions and Effects
class AnimationManager {
    constructor() {
        this.observers = new Map();
        this.animatedElements = new Set();
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupLoadingAnimations();
    }

    // Intersection Observer for scroll-triggered animations
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements with data-aos attributes
        document.querySelectorAll('[data-aos]').forEach(element => {
            observer.observe(element);
        });

        this.observers.set('scroll', observer);
    }

    triggerAnimation(element) {
        const animationType = element.getAttribute('data-aos');
        const delay = element.getAttribute('data-aos-delay') || 0;

        setTimeout(() => {
            element.classList.add('aos-animate');
            this.animatedElements.add(element);
        }, parseInt(delay));
    }

    // Smooth scroll animations
    setupScrollAnimations() {
        // Parallax effect for background elements
        window.addEventListener('scroll', this.throttle(() => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');
            
            parallaxElements.forEach(element => {
                const speed = element.getAttribute('data-speed') || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            // Update navigation opacity based on scroll
            this.updateNavigationOpacity(scrolled);
        }, 16));
    }

    updateNavigationOpacity(scrolled) {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            const opacity = Math.min(scrolled / 100, 0.95);
            navbar.style.background = `rgba(10, 10, 10, ${opacity})`;
        }
    }

    // Hover effects and micro-interactions
    setupHoverEffects() {
        // Feature cards hover effect
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.animateCardHover(card, true);
            });

            card.addEventListener('mouseleave', () => {
                this.animateCardHover(card, false);
            });
        });

        // Button hover effects
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.animateButtonHover(button, true);
            });

            button.addEventListener('mouseleave', () => {
                this.animateButtonHover(button, false);
            });
        });

        // Navigation link effects
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('mouseenter', () => {
                this.animateNavLink(link, true);
            });

            link.addEventListener('mouseleave', () => {
                this.animateNavLink(link, false);
            });
        });
    }

    animateCardHover(card, isHover) {
        const icon = card.querySelector('.feature-icon');
        const title = card.querySelector('.feature-title');
        
        if (isHover) {
            card.style.transform = 'translateY(-10px) scale(1.02)';
            card.style.boxShadow = '0 20px 40px rgba(0, 212, 255, 0.3)';
            
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
            
            if (title) {
                title.style.color = '#00d4ff';
            }
        } else {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.1)';
            
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
            
            if (title) {
                title.style.color = '#ffffff';
            }
        }
    }

    animateButtonHover(button, isHover) {
        if (isHover) {
            button.style.transform = 'translateY(-3px) scale(1.05)';
            button.style.boxShadow = '0 15px 30px rgba(255, 215, 0, 0.4)';
        } else {
            button.style.transform = 'translateY(0) scale(1)';
            button.style.boxShadow = '0 5px 15px rgba(255, 215, 0, 0.2)';
        }
    }

    animateNavLink(link, isHover) {
        if (isHover) {
            link.style.transform = 'translateY(-2px)';
            link.style.textShadow = '0 0 10px rgba(0, 212, 255, 0.5)';
        } else {
            link.style.transform = 'translateY(0)';
            link.style.textShadow = 'none';
        }
    }

    // Loading animations
    setupLoadingAnimations() {
        // Stagger animation for multiple elements
        this.staggerElements('.feature-card', 200);
        this.staggerElements('.theme-tag', 100);
        this.staggerElements('.pattern-tag', 150);
    }

    staggerElements(selector, delay) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
            element.style.animationDelay = `${index * delay}ms`;
        });
    }

    // Smooth page transitions
    setupPageTransitions() {
        document.querySelectorAll('a[href]').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                // Only handle internal links
                if (href.startsWith('#') || href.includes('.html')) {
                    e.preventDefault();
                    this.transitionToPage(href);
                }
            });
        });
    }

    transitionToPage(href) {
        // Fade out current content
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            if (href.startsWith('#')) {
                // Smooth scroll to section
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
                
                // Fade back in
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            } else {
                // Navigate to new page
                window.location.href = href;
            }
        }, 300);
    }

    // Utility functions
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public methods for external control
    animateElement(element, animation, duration = 600) {
        return new Promise((resolve) => {
            element.style.animation = `${animation} ${duration}ms ease-out`;
            
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }

    fadeIn(element, duration = 600) {
        return new Promise((resolve) => {
            element.style.opacity = '0';
            element.style.transition = `opacity ${duration}ms ease-out`;
            
            requestAnimationFrame(() => {
                element.style.opacity = '1';
                
                setTimeout(() => {
                    element.style.transition = '';
                    resolve();
                }, duration);
            });
        });
    }

    fadeOut(element, duration = 600) {
        return new Promise((resolve) => {
            element.style.transition = `opacity ${duration}ms ease-out`;
            element.style.opacity = '0';
            
            setTimeout(() => {
                element.style.transition = '';
                resolve();
            }, duration);
        });
    }

    slideIn(element, direction = 'up', duration = 600) {
        return new Promise((resolve) => {
            const transforms = {
                up: 'translateY(30px)',
                down: 'translateY(-30px)',
                left: 'translateX(30px)',
                right: 'translateX(-30px)'
            };

            element.style.transform = transforms[direction];
            element.style.opacity = '0';
            element.style.transition = `all ${duration}ms ease-out`;
            
            requestAnimationFrame(() => {
                element.style.transform = 'translate(0)';
                element.style.opacity = '1';
                
                setTimeout(() => {
                    element.style.transition = '';
                    resolve();
                }, duration);
            });
        });
    }

    // Cleanup method
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animatedElements.clear();
    }
}

// Initialize animation manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.animationManager = new AnimationManager();
    
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationManager;
}
