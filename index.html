<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>「慧视」AI金融洞察引擎 | HuiShi AI Financial Insight Engine</title>
    <meta name="description" content="AI-powered financial insights with K-line pattern analysis and narrative intelligence">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Language Toggle -->
    <div class="language-toggle">
        <button id="langToggle" class="lang-btn">
            <span class="lang-text" data-zh="EN" data-en="中文">EN</span>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-text" data-zh="「慧视」" data-en="HuiShi">「慧视」</span>
                <span class="logo-subtitle" data-zh="AI金融洞察" data-en="AI Insights">AI金融洞察</span>
            </div>
            <div class="nav-links">
                <a href="#home" class="nav-link" data-zh="首页" data-en="Home">首页</a>
                <a href="analysis.html" class="nav-link" data-zh="AI分析" data-en="AI Analysis">AI分析</a>
                <a href="#features" class="nav-link" data-zh="功能" data-en="Features">功能</a>
                <a href="#web3" class="nav-link" data-zh="Web3愿景" data-en="Web3 Vision">Web3愿景</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="neural-background">
            <canvas id="neuralCanvas"></canvas>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="title-main" data-zh="「慧视」AI金融洞察引擎" data-en="HuiShi AI Financial Insight Engine">「慧视」AI金融洞察引擎</span>
            </h1>
            <p class="hero-subtitle" data-zh="洞悉市场深层模式，AI驱动的智能决策洞察" data-en="Unveil the Market's Deep Patterns. AI-Powered Insights for Smarter Decisions.">洞悉市场深层模式，AI驱动的智能决策洞察</p>
            
            <div class="hero-features">
                <span class="feature-item" data-zh="K线模式洞察" data-en="K-Line Pattern Insight">K线模式洞察</span>
                <span class="feature-separator">|</span>
                <span class="feature-item" data-zh="金融报告叙事" data-en="Financial Report Narratives">金融报告叙事</span>
                <span class="feature-separator">|</span>
                <span class="feature-item" data-zh="Web3未来探索" data-en="Pioneering Web3 Future">Web3未来探索</span>
            </div>

            <button class="cta-button" data-zh="探索AI洞察" data-en="Explore AI Insights">
                <span>探索AI洞察</span>
                <div class="button-glow"></div>
            </button>

            <p class="disclaimer" data-zh="AI分析仅供演示，不构成投资建议" data-en="AI analysis for demonstration only. Not investment advice.">AI分析仅供演示，不构成投资建议</p>
        </div>
    </section>

    <!-- Key Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title" data-zh="核心功能" data-en="Key Features">核心功能</h2>
            
            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <svg class="icon-kline" viewBox="0 0 24 24">
                            <path d="M3 17h18v2H3v-2zm0-6h18v2H3v-2zm0-6h18v2H3V5z"/>
                            <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3 class="feature-title" data-zh="K线模式洞察" data-en="K-Line Pattern Insight">K线模式洞察</h3>
                    <p class="feature-description" data-zh="AI识别关键技术形态，揭示市场隐藏的价格模式" data-en="AI identifies key technical patterns, revealing hidden market price formations">AI识别关键技术形态，揭示市场隐藏的价格模式</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <svg class="icon-narrative" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title" data-zh="金融叙事引擎" data-en="Financial Narrative Engine">金融叙事引擎</h3>
                    <p class="feature-description" data-zh="深度解析财报核心叙事，智能识别市场情绪脉络" data-en="Deep analysis of financial report narratives with intelligent sentiment recognition">深度解析财报核心叙事，智能识别市场情绪脉络</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <svg class="icon-ai" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            <circle cx="12" cy="8" r="2" fill="currentColor" opacity="0.8"/>
                            <circle cx="8" cy="16" r="1.5" fill="currentColor" opacity="0.6"/>
                            <circle cx="16" cy="16" r="1.5" fill="currentColor" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3 class="feature-title" data-zh="智能概率分析" data-en="Intelligent Probability Analysis">智能概率分析</h3>
                    <p class="feature-description" data-zh="基于历史数据的概率统计，为决策提供量化参考" data-en="Historical probability statistics providing quantified decision support">基于历史数据的概率统计，为决策提供量化参考</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Web3 Vision Section -->
    <section id="web3" class="web3-vision">
        <div class="container">
            <h2 class="section-title" data-zh="探索未来：AI与Web3" data-en="Exploring the Future: AI & Web3">探索未来：AI与Web3</h2>
            <div class="web3-content">
                <div class="web3-text">
                    <p data-zh="我们正在探索AI生成洞察与Web3技术的深度融合，构建去中心化的金融智能生态系统。未来，AI洞察将作为数字资产在区块链上验证和交易，为金融分析带来全新的信任机制。" 
                       data-en="We are exploring the deep integration of AI-generated insights with Web3 technology, building a decentralized financial intelligence ecosystem. In the future, AI insights will be verified and traded as digital assets on blockchain, bringing new trust mechanisms to financial analysis.">
                       我们正在探索AI生成洞察与Web3技术的深度融合，构建去中心化的金融智能生态系统。未来，AI洞察将作为数字资产在区块链上验证和交易，为金融分析带来全新的信任机制。
                    </p>
                </div>
                <div class="web3-visual">
                    <div class="blockchain-nodes">
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-disclaimer">
                    <p data-zh="本网站内容仅用于AI技术演示，不构成投资建议。" data-en="This website content is for AI technology demonstration only and does not constitute investment advice.">本网站内容仅用于AI技术演示，不构成投资建议。</p>
                </div>
                <div class="footer-links">
                    <span data-zh="© 2024 慧视AI. 保留所有权利。" data-en="© 2024 HuiShi AI. All rights reserved.">© 2024 慧视AI. 保留所有权利。</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="scripts/language.js"></script>
    <script src="scripts/animations.js"></script>
    <script src="scripts/neural-background.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
