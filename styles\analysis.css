/* Analysis Page Specific Styles */

.analysis-page {
    background: var(--bg-primary);
    min-height: 100vh;
}

/* Analysis Navigation */
.analysis-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    padding: 1rem 0;
}

.analysis-navbar .nav-container {
    display: grid;
    grid-template-columns: 200px 1fr 200px;
    align-items: center;
    gap: 2rem;
}

.stock-selector {
    display: flex;
    justify-content: center;
}

.stock-dropdown {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid var(--tech-blue);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-family: var(--font-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 250px;
    text-align: center;
}

.stock-dropdown:hover,
.stock-dropdown:focus {
    box-shadow: var(--glow-blue);
    outline: none;
    background: rgba(26, 26, 26, 1);
}

.stock-dropdown option {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.5rem;
}

/* Main Analysis Layout */
.analysis-main {
    margin-top: 100px;
    padding: 2rem 0;
}

.analysis-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 3rem;
}

/* Charts Section */
.charts-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.chart-container {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0.6;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: var(--tech-blue);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    font-family: var(--font-primary);
    font-weight: 500;
    transition: all 0.3s ease;
}

.chart-btn:hover,
.chart-btn.active {
    background: rgba(0, 212, 255, 0.2);
    box-shadow: var(--glow-blue);
}

.chart-wrapper {
    position: relative;
    height: 400px;
}

.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}

/* AI Annotations */
.ai-annotations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.pattern-highlight {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid var(--ai-green);
    border-radius: 50%;
    background: rgba(0, 255, 136, 0.1);
    animation: patternPulse 2s ease-in-out infinite;
}

.pattern-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 255, 136, 0.9);
    color: var(--bg-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.support-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--tech-blue), transparent);
    animation: lineGlow 3s ease-in-out infinite;
}

/* Volume Chart */
.volume-chart {
    background: rgba(26, 26, 26, 0.6);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    height: 200px;
}

.volume-title {
    color: var(--mystery-purple);
    margin-bottom: 1rem;
    font-weight: 600;
}

/* AI Interface */
.ai-interface {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.ai-console {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(15px);
    box-shadow: var(--glow-blue);
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.console-title {
    font-size: 1.4rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--ai-green);
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: var(--ai-green);
    border-radius: 50%;
    animation: statusPulse 1.5s ease-in-out infinite;
}

/* Analysis Modules */
.analysis-module {
    background: rgba(42, 42, 42, 0.6);
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.analysis-module:hover {
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);
}

.module-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Insight Content */
.insight-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.insight-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.insight-item label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.insight-text {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Pattern Tags */
.pattern-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.pattern-tag {
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid;
}

.pattern-tag.bullish {
    background: rgba(0, 255, 136, 0.1);
    border-color: var(--ai-green);
    color: var(--ai-green);
}

.pattern-tag.neutral {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--tech-blue);
    color: var(--tech-blue);
}

.pattern-tag.bearish {
    background: rgba(255, 100, 100, 0.1);
    border-color: #ff6464;
    color: #ff6464;
}

/* Price Zones */
.price-zones {
    display: flex;
    gap: 1rem;
}

.price-zone {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    min-width: 80px;
}

.price-zone.support {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid var(--ai-green);
}

.price-zone.resistance {
    background: rgba(255, 100, 100, 0.1);
    border: 1px solid #ff6464;
}

.zone-label {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.zone-price {
    font-family: var(--font-mono);
    font-weight: 600;
    font-size: 1rem;
}

/* Probability Analysis */
.probability-analysis {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.prob-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.probability-bars {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.prob-bar {
    display: grid;
    grid-template-columns: 60px 1fr 50px;
    align-items: center;
    gap: 1rem;
}

.prob-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.prob-visual {
    position: relative;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.prob-fill {
    height: 100%;
    border-radius: 10px;
    position: relative;
    transition: width 1s ease;
}

.prob-fill.up {
    background: linear-gradient(90deg, var(--ai-green), #00cc6a);
}

.prob-fill.down {
    background: linear-gradient(90deg, #ff6464, #ff4444);
}

.prob-fill.sideways {
    background: linear-gradient(90deg, var(--tech-blue), #0099cc);
}

.prob-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: particleFlow 2s linear infinite;
}

.prob-value {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-primary);
    text-align: right;
}

.prob-disclaimer {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    text-align: center;
    margin-top: 1rem;
    font-style: italic;
}

/* Narrative Content */
.narrative-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.narrative-item {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.narrative-summary {
    color: var(--text-secondary);
    line-height: 1.6;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 3px solid var(--tech-blue);
}

/* Sentiment Visual */
.sentiment-visual {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sentiment-bar {
    position: relative;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.sentiment-fill {
    height: 100%;
    border-radius: 15px;
    position: relative;
}

.sentiment-fill.positive {
    background: linear-gradient(90deg, var(--ai-green), #00ff88, var(--ai-green));
    animation: sentimentFlow 3s ease-in-out infinite;
}

.sentiment-wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: waveFlow 2s linear infinite;
}

.sentiment-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--ai-green);
}

/* Theme Cloud */
.theme-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.theme-tag {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
}

.theme-tag.high {
    font-size: 1rem;
    background: rgba(0, 255, 136, 0.2);
    color: var(--ai-green);
    box-shadow: var(--glow-green);
}

.theme-tag.medium {
    font-size: 0.9rem;
    background: rgba(0, 212, 255, 0.2);
    color: var(--tech-blue);
}

.theme-tag.low {
    font-size: 0.8rem;
    background: rgba(139, 92, 246, 0.2);
    color: var(--mystery-purple);
}

.source-info {
    color: var(--text-tertiary);
    font-size: 0.9rem;
    font-style: italic;
}

/* Bottom Disclaimer */
.bottom-disclaimer {
    background: rgba(10, 10, 10, 0.95);
    border-top: 1px solid rgba(0, 212, 255, 0.2);
    padding: 1.5rem 2rem;
    text-align: center;
    position: sticky;
    bottom: 0;
    backdrop-filter: blur(10px);
}

.bottom-disclaimer p {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    max-width: 800px;
    margin: 0 auto;
}

/* Animations */
@keyframes patternPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
}

@keyframes lineGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes particleFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(200%); }
}

@keyframes sentimentFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes waveFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(200%); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analysis-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .ai-interface {
        position: static;
    }
}

@media (max-width: 768px) {
    .analysis-navbar .nav-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }
    
    .stock-dropdown {
        min-width: 200px;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .price-zones {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .prob-bar {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: center;
    }
}
