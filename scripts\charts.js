// Chart Management for K-line and Volume Analysis
class ChartManager {
    constructor() {
        this.charts = {};
        this.currentStock = '600519';
        this.currentPeriod = 'day';
        this.mockData = this.generateMockData();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.updateCharts();
    }

    setupEventListeners() {
        // Stock selector
        const stockSelect = document.getElementById('stockSelect');
        if (stockSelect) {
            stockSelect.addEventListener('change', (e) => {
                this.currentStock = e.target.value;
                this.updateCharts();
            });
        }

        // Period buttons
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentPeriod = e.target.getAttribute('data-period');
                this.updateCharts();
            });
        });

        // Language change listener
        window.addEventListener('languageChanged', () => {
            this.updateChartLabels();
        });
    }

    generateMockData() {
        const stocks = {
            '600519': { name: '贵州茅台', basePrice: 1700 },
            '000858': { name: '五粮液', basePrice: 180 },
            '000001': { name: '平安银行', basePrice: 12 },
            '600036': { name: '招商银行', basePrice: 35 }
        };

        const data = {};
        
        Object.keys(stocks).forEach(stockCode => {
            const stock = stocks[stockCode];
            data[stockCode] = {
                day: this.generateKlineData(stock.basePrice, 30, 'day'),
                week: this.generateKlineData(stock.basePrice, 20, 'week'),
                month: this.generateKlineData(stock.basePrice, 12, 'month')
            };
        });

        return data;
    }

    generateKlineData(basePrice, periods, type) {
        const data = [];
        let currentPrice = basePrice;
        const now = new Date();
        
        for (let i = periods - 1; i >= 0; i--) {
            const date = new Date(now);
            
            if (type === 'day') {
                date.setDate(date.getDate() - i);
            } else if (type === 'week') {
                date.setDate(date.getDate() - i * 7);
            } else if (type === 'month') {
                date.setMonth(date.getMonth() - i);
            }

            // Generate realistic price movement
            const volatility = basePrice * 0.02; // 2% volatility
            const change = (Math.random() - 0.5) * volatility;
            currentPrice += change;
            
            const open = currentPrice;
            const high = open + Math.random() * volatility * 0.5;
            const low = open - Math.random() * volatility * 0.5;
            const close = low + Math.random() * (high - low);
            const volume = Math.floor(Math.random() * 1000000) + 500000;

            data.push({
                date: date.toISOString().split('T')[0],
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2)),
                volume: volume
            });

            currentPrice = close;
        }

        return data;
    }

    initializeCharts() {
        this.initKlineChart();
        this.initVolumeChart();
    }

    initKlineChart() {
        const canvas = document.getElementById('klineChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.kline = new Chart(ctx, {
            type: 'candlestick',
            data: {
                datasets: [{
                    label: 'K-Line',
                    data: [],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(26, 26, 26, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#b0b0b0',
                        borderColor: '#00d4ff',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const data = context.raw;
                                return [
                                    `开盘: ${data.o}`,
                                    `最高: ${data.h}`,
                                    `最低: ${data.l}`,
                                    `收盘: ${data.c}`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day'
                        },
                        grid: {
                            color: 'rgba(0, 212, 255, 0.1)'
                        },
                        ticks: {
                            color: '#b0b0b0'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 212, 255, 0.1)'
                        },
                        ticks: {
                            color: '#b0b0b0'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    initVolumeChart() {
        const canvas = document.getElementById('volumeChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.volume = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Volume',
                    data: [],
                    backgroundColor: 'rgba(139, 92, 246, 0.6)',
                    borderColor: '#8b5cf6',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(26, 26, 26, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#b0b0b0',
                        borderColor: '#8b5cf6',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(139, 92, 246, 0.1)'
                        },
                        ticks: {
                            color: '#b0b0b0'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(139, 92, 246, 0.1)'
                        },
                        ticks: {
                            color: '#b0b0b0',
                            callback: function(value) {
                                return (value / 1000000).toFixed(1) + 'M';
                            }
                        }
                    }
                }
            }
        });
    }

    updateCharts() {
        const stockData = this.mockData[this.currentStock];
        if (!stockData) return;

        const periodData = stockData[this.currentPeriod];
        this.updateKlineChart(periodData);
        this.updateVolumeChart(periodData);
        this.updateAIAnnotations(periodData);
    }

    updateKlineChart(data) {
        if (!this.charts.kline) return;

        // Convert data to Chart.js candlestick format
        const chartData = data.map(item => ({
            x: item.date,
            o: item.open,
            h: item.high,
            l: item.low,
            c: item.close
        }));

        this.charts.kline.data.labels = data.map(item => item.date);
        this.charts.kline.data.datasets[0].data = chartData;
        
        // Add smooth animation
        this.charts.kline.options.animation = {
            duration: 800,
            easing: 'easeInOutQuart'
        };
        
        this.charts.kline.update();
    }

    updateVolumeChart(data) {
        if (!this.charts.volume) return;

        this.charts.volume.data.labels = data.map(item => item.date);
        this.charts.volume.data.datasets[0].data = data.map(item => item.volume);
        
        // Add smooth animation
        this.charts.volume.options.animation = {
            duration: 800,
            easing: 'easeInOutQuart'
        };
        
        this.charts.volume.update();
    }

    updateAIAnnotations(data) {
        // Simulate AI pattern detection
        const patterns = this.detectPatterns(data);
        this.displayPatterns(patterns);
        this.updateProbabilityAnalysis(data);
    }

    detectPatterns(data) {
        // Mock AI pattern detection
        const patterns = [];
        
        // Look for hammer pattern (simplified)
        for (let i = 1; i < data.length; i++) {
            const current = data[i];
            const previous = data[i - 1];
            
            const bodySize = Math.abs(current.close - current.open);
            const lowerShadow = Math.min(current.open, current.close) - current.low;
            const upperShadow = current.high - Math.max(current.open, current.close);
            
            // Hammer pattern detection
            if (lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5 && current.close < previous.close) {
                patterns.push({
                    type: 'hammer',
                    date: current.date,
                    confidence: 0.8,
                    position: i
                });
            }
        }
        
        return patterns;
    }

    displayPatterns(patterns) {
        const patternTags = document.querySelector('.pattern-tags');
        if (!patternTags) return;

        // Clear existing patterns
        patternTags.innerHTML = '';

        patterns.forEach(pattern => {
            const tag = document.createElement('span');
            tag.className = 'pattern-tag bullish';
            
            const isZh = window.languageManager?.getCurrentLanguage() === 'zh';
            const patternNames = {
                hammer: isZh ? '锤头线' : 'Hammer'
            };
            
            tag.textContent = patternNames[pattern.type] || pattern.type;
            patternTags.appendChild(tag);
        });
    }

    updateProbabilityAnalysis(data) {
        // Mock probability calculation based on recent trends
        const recentData = data.slice(-5);
        const upDays = recentData.filter(d => d.close > d.open).length;
        const totalDays = recentData.length;
        
        // Simple probability calculation
        const upProb = Math.min(Math.max((upDays / totalDays) * 100 + Math.random() * 20 - 10, 15), 85);
        const downProb = Math.min(Math.max(100 - upProb - Math.random() * 20, 10), 75);
        const sidewaysProb = 100 - upProb - downProb;

        this.updateProbabilityBars({
            up: Math.round(upProb),
            down: Math.round(downProb),
            sideways: Math.round(sidewaysProb)
        });
    }

    updateProbabilityBars(probabilities) {
        const probBars = document.querySelectorAll('.prob-bar');
        
        probBars.forEach(bar => {
            const fill = bar.querySelector('.prob-fill');
            const value = bar.querySelector('.prob-value');
            
            if (fill.classList.contains('up')) {
                fill.style.width = `${probabilities.up}%`;
                value.textContent = `${probabilities.up}%`;
            } else if (fill.classList.contains('down')) {
                fill.style.width = `${probabilities.down}%`;
                value.textContent = `${probabilities.down}%`;
            } else if (fill.classList.contains('sideways')) {
                fill.style.width = `${probabilities.sideways}%`;
                value.textContent = `${probabilities.sideways}%`;
            }
        });
    }

    updateChartLabels() {
        // Update chart labels based on current language
        const isZh = window.languageManager?.getCurrentLanguage() === 'zh';
        
        if (this.charts.kline) {
            this.charts.kline.options.plugins.tooltip.callbacks.label = function(context) {
                const data = context.raw;
                if (isZh) {
                    return [
                        `开盘: ${data.o}`,
                        `最高: ${data.h}`,
                        `最低: ${data.l}`,
                        `收盘: ${data.c}`
                    ];
                } else {
                    return [
                        `Open: ${data.o}`,
                        `High: ${data.h}`,
                        `Low: ${data.l}`,
                        `Close: ${data.c}`
                    ];
                }
            };
            this.charts.kline.update();
        }
    }

    // Public methods
    refreshData() {
        this.mockData = this.generateMockData();
        this.updateCharts();
    }

    setStock(stockCode) {
        this.currentStock = stockCode;
        this.updateCharts();
    }

    setPeriod(period) {
        this.currentPeriod = period;
        this.updateCharts();
    }

    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}

// Custom candlestick chart type for Chart.js
if (typeof Chart !== 'undefined') {
    Chart.register({
        id: 'candlestick',
        beforeInit: function(chart) {
            // Custom candlestick implementation would go here
            // For now, we'll use line chart as fallback
            chart.config.type = 'line';
        }
    });
}

// Initialize chart manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('klineChart')) {
        window.chartManager = new ChartManager();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartManager;
}
