/* Animation Styles and Effects */

/* AOS (Animate On Scroll) Custom Animations */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
    transition-duration: 0.6s;
    transition-timing-function: ease-out;
}

[data-aos].aos-animate {
    opacity: 1;
}

/* Fade Up Animation */
[data-aos="fade-up"] {
    transform: translateY(30px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

/* Fade Left Animation */
[data-aos="fade-left"] {
    transform: translateX(30px);
}

[data-aos="fade-left"].aos-animate {
    transform: translateX(0);
}

/* Fade Right Animation */
[data-aos="fade-right"] {
    transform: translateX(-30px);
}

[data-aos="fade-right"].aos-animate {
    transform: translateX(0);
}

/* Scale Animation */
[data-aos="scale"] {
    transform: scale(0.8);
}

[data-aos="scale"].aos-animate {
    transform: scale(1);
}

/* Floating Elements */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Glowing Effects */
.glow-blue {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    animation: glowPulse 2s ease-in-out infinite alternate;
}

.glow-green {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
    animation: glowPulse 2s ease-in-out infinite alternate;
}

.glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
    animation: glowPulse 2s ease-in-out infinite alternate;
}

@keyframes glowPulse {
    from {
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(0, 212, 255, 0.7);
    }
}

/* Text Animations */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--tech-blue);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--tech-blue);
    }
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid var(--tech-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Neural Network Animation */
.neural-pulse {
    animation: neuralPulse 2s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* Data Flow Animation */
.data-flow {
    position: relative;
    overflow: hidden;
}

.data-flow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.4), transparent);
    animation: dataFlow 2s linear infinite;
}

@keyframes dataFlow {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Particle Effects */
.particles {
    position: relative;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--tech-blue);
    border-radius: 50%;
    animation: particleMove 4s linear infinite;
}

@keyframes particleMove {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    border-color: var(--tech-blue);
}

/* Scale on Hover */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Slide Animations */
.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Bounce Animation */
.bounce {
    animation: bounce 1s ease infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Shake Animation */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Gradient Animation */
.gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Matrix Rain Effect */
.matrix-rain {
    position: relative;
    overflow: hidden;
}

.matrix-char {
    position: absolute;
    color: var(--ai-green);
    font-family: var(--font-mono);
    font-size: 14px;
    animation: matrixFall 3s linear infinite;
}

@keyframes matrixFall {
    0% {
        transform: translateY(-100px);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Breathing Animation */
.breathe {
    animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.3);
    transform: translate(-50%, -50%);
    animation: ripple 1.5s ease-out infinite;
}

@keyframes ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Smooth Transitions */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-slow {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Entrance Animations */
.entrance-fade {
    animation: entranceFade 0.8s ease-out;
}

@keyframes entranceFade {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.entrance-scale {
    animation: entranceScale 0.6s ease-out;
}

@keyframes entranceScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s infinite;
}

@keyframes skeleton {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Utility Classes */
.animate-once {
    animation-iteration-count: 1;
}

.animate-infinite {
    animation-iteration-count: infinite;
}

.animate-pause {
    animation-play-state: paused;
}

.animate-play {
    animation-play-state: running;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
