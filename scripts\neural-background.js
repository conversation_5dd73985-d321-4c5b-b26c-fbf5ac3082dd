// Neural Network Background Animation
class NeuralBackground {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        if (!this.canvas) return;
        
        this.ctx = this.canvas.getContext('2d');
        this.nodes = [];
        this.connections = [];
        this.animationId = null;
        this.mousePos = { x: 0, y: 0 };
        
        this.config = {
            nodeCount: 50,
            maxDistance: 150,
            nodeSize: 2,
            connectionOpacity: 0.3,
            nodeOpacity: 0.8,
            animationSpeed: 0.5,
            mouseInfluence: 100,
            colors: {
                nodes: '#00d4ff',
                connections: '#8b5cf6',
                highlight: '#00ff88'
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.createNodes();
        this.setupEventListeners();
        this.animate();
    }
    
    setupCanvas() {
        const resizeCanvas = () => {
            const rect = this.canvas.parentElement.getBoundingClientRect();
            this.canvas.width = rect.width;
            this.canvas.height = rect.height;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }
    
    createNodes() {
        this.nodes = [];
        
        for (let i = 0; i < this.config.nodeCount; i++) {
            this.nodes.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * this.config.animationSpeed,
                vy: (Math.random() - 0.5) * this.config.animationSpeed,
                size: Math.random() * this.config.nodeSize + 1,
                opacity: Math.random() * this.config.nodeOpacity + 0.2,
                pulse: Math.random() * Math.PI * 2
            });
        }
    }
    
    setupEventListeners() {
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mousePos.x = e.clientX - rect.left;
            this.mousePos.y = e.clientY - rect.top;
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.mousePos.x = -1000;
            this.mousePos.y = -1000;
        });
    }
    
    updateNodes() {
        this.nodes.forEach(node => {
            // Update position
            node.x += node.vx;
            node.y += node.vy;
            
            // Bounce off edges
            if (node.x < 0 || node.x > this.canvas.width) {
                node.vx *= -1;
                node.x = Math.max(0, Math.min(this.canvas.width, node.x));
            }
            if (node.y < 0 || node.y > this.canvas.height) {
                node.vy *= -1;
                node.y = Math.max(0, Math.min(this.canvas.height, node.y));
            }
            
            // Mouse interaction
            const dx = this.mousePos.x - node.x;
            const dy = this.mousePos.y - node.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < this.config.mouseInfluence) {
                const force = (this.config.mouseInfluence - distance) / this.config.mouseInfluence;
                node.vx += (dx / distance) * force * 0.01;
                node.vy += (dy / distance) * force * 0.01;
            }
            
            // Update pulse for breathing effect
            node.pulse += 0.02;
            
            // Limit velocity
            const maxVel = this.config.animationSpeed * 2;
            node.vx = Math.max(-maxVel, Math.min(maxVel, node.vx));
            node.vy = Math.max(-maxVel, Math.min(maxVel, node.vy));
            
            // Apply friction
            node.vx *= 0.99;
            node.vy *= 0.99;
        });
    }
    
    drawConnections() {
        this.connections = [];
        
        for (let i = 0; i < this.nodes.length; i++) {
            for (let j = i + 1; j < this.nodes.length; j++) {
                const nodeA = this.nodes[i];
                const nodeB = this.nodes[j];
                
                const dx = nodeA.x - nodeB.x;
                const dy = nodeA.y - nodeB.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < this.config.maxDistance) {
                    const opacity = (1 - distance / this.config.maxDistance) * this.config.connectionOpacity;
                    
                    // Check if connection is near mouse
                    const mouseDistance = this.getDistanceToLine(
                        this.mousePos.x, this.mousePos.y,
                        nodeA.x, nodeA.y, nodeB.x, nodeB.y
                    );
                    
                    let color = this.config.colors.connections;
                    let lineWidth = 1;
                    
                    if (mouseDistance < 50) {
                        color = this.config.colors.highlight;
                        lineWidth = 2;
                    }
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(nodeA.x, nodeA.y);
                    this.ctx.lineTo(nodeB.x, nodeB.y);
                    this.ctx.strokeStyle = this.hexToRgba(color, opacity);
                    this.ctx.lineWidth = lineWidth;
                    this.ctx.stroke();
                    
                    this.connections.push({ nodeA, nodeB, distance, opacity });
                }
            }
        }
    }
    
    drawNodes() {
        this.nodes.forEach(node => {
            const dx = this.mousePos.x - node.x;
            const dy = this.mousePos.y - node.y;
            const mouseDistance = Math.sqrt(dx * dx + dy * dy);
            
            let size = node.size;
            let color = this.config.colors.nodes;
            let opacity = node.opacity;
            
            // Mouse proximity effect
            if (mouseDistance < this.config.mouseInfluence) {
                const influence = 1 - mouseDistance / this.config.mouseInfluence;
                size += influence * 3;
                color = this.config.colors.highlight;
                opacity += influence * 0.5;
            }
            
            // Breathing effect
            const pulseSize = size + Math.sin(node.pulse) * 0.5;
            const pulseOpacity = opacity + Math.sin(node.pulse) * 0.1;
            
            // Draw node glow
            const gradient = this.ctx.createRadialGradient(
                node.x, node.y, 0,
                node.x, node.y, pulseSize * 3
            );
            gradient.addColorStop(0, this.hexToRgba(color, pulseOpacity * 0.8));
            gradient.addColorStop(0.5, this.hexToRgba(color, pulseOpacity * 0.3));
            gradient.addColorStop(1, this.hexToRgba(color, 0));
            
            this.ctx.beginPath();
            this.ctx.arc(node.x, node.y, pulseSize * 3, 0, Math.PI * 2);
            this.ctx.fillStyle = gradient;
            this.ctx.fill();
            
            // Draw node core
            this.ctx.beginPath();
            this.ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2);
            this.ctx.fillStyle = this.hexToRgba(color, pulseOpacity);
            this.ctx.fill();
        });
    }
    
    getDistanceToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) return Math.sqrt(A * A + B * B);
        
        let param = dot / lenSq;
        
        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const xx = x1 + param * C;
            const yy = y1 + param * D;
            const dx = px - xx;
            const dy = py - yy;
            return Math.sqrt(dx * dx + dy * dy);
        }
    }
    
    hexToRgba(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.updateNodes();
        this.drawConnections();
        this.drawNodes();
        
        this.animationId = requestAnimationFrame(() => this.animate());
    }
    
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        window.removeEventListener('resize', this.setupCanvas);
    }
    
    // Public methods for controlling the animation
    pause() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    resume() {
        if (!this.animationId) {
            this.animate();
        }
    }
    
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        if (newConfig.nodeCount && newConfig.nodeCount !== this.nodes.length) {
            this.createNodes();
        }
    }
}

// Initialize neural background when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const neuralCanvas = document.getElementById('neuralCanvas');
    if (neuralCanvas) {
        window.neuralBackground = new NeuralBackground('neuralCanvas');
        
        // Pause animation when page is not visible to save resources
        document.addEventListener('visibilitychange', () => {
            if (window.neuralBackground) {
                if (document.hidden) {
                    window.neuralBackground.pause();
                } else {
                    window.neuralBackground.resume();
                }
            }
        });
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NeuralBackground;
}
